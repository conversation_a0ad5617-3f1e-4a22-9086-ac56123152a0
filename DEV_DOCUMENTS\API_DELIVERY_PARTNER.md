# 🚚 Delivery Partner API Documentation

> **Delivery management API for delivery partners and fleet operators**

## 🎯 **Overview**

The Delivery Partner API provides comprehensive delivery management functionality including order pickup/delivery, location tracking, earnings management, and performance analytics. This API powers the delivery partner mobile application.

---

## 🔐 **Authentication & Authorization**

### **Required Authentication**
```bash
Authorization: Bearer <jwt-token>
```

### **Authorized Role**
- **Delivery Partner**: All endpoints require delivery partner role

---

## 🚚 **API Endpoints**

### **Base URL**
```
http://localhost:3000/api/v1/delivery-partner
```

---

## 👤 **1. Profile Management**

### **GET /profile**
Get delivery partner profile and performance overview.

**Request:**
```bash
GET /api/v1/delivery-partner/profile
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "phone": "+************",
      "role": "delivery_partner"
    },
    "profile": {
      "name": "Delivery Partner",
      "vehicleInfo": {
        "type": "bike",
        "number": "MH01AB1234",
        "model": "Honda Activa"
      },
      "documents": {
        "drivingLicense": "verified",
        "vehicleRC": "verified",
        "aadhar": "verified"
      }
    },
    "location": {
      "coordinates": [72.8777, 19.0760],
      "lastUpdated": "2024-12-01T15:30:00Z"
    },
    "status": {
      "isOnline": true,
      "isAvailable": true,
      "currentOrder": null
    },
    "performance": {
      "rating": 4.5,
      "totalDeliveries": 150,
      "completionRate": 95.5,
      "avgDeliveryTime": 25
    },
    "earnings": {
      "todayEarnings": 500,
      "weeklyEarnings": 3500,
      "monthlyEarnings": 15000
    }
  }
}
```

### **PUT /profile**
Update delivery partner profile.

**Request:**
```bash
PUT /api/v1/delivery-partner/profile
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "profile": {
    "name": "Updated Delivery Partner",
    "vehicleInfo": {
      "type": "bike",
      "number": "MH01AB1234",
      "model": "Honda Activa 6G"
    }
  }
}
```

---

## 📍 **2. Location & Status Management**

### **PUT /status**
Update online/offline status.

**Request:**
```bash
PUT /api/v1/delivery-partner/status
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "isOnline": true,
  "isAvailable": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "isOnline": true,
    "isAvailable": true,
    "timestamp": "2024-12-01T15:30:00Z"
  }
}
```

### **PUT /location**
Update current location.

**Request:**
```bash
PUT /api/v1/delivery-partner/location
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "coordinates": [72.8777, 19.0760],
  "accuracy": 10,
  "heading": 45,
  "speed": 25
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "location": {
      "coordinates": [72.8777, 19.0760],
      "accuracy": 10,
      "timestamp": "2024-12-01T15:30:00Z"
    }
  }
}
```

---

## 📦 **3. Order Management**

### **GET /orders/available**
Get available orders for pickup.

**Request:**
```bash
GET /api/v1/delivery-partner/orders/available?radius=5
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `radius` (optional): Search radius in km (default: 5)
- `limit` (optional): Number of results (default: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "availableOrders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-20241201-1234",
        "restaurant": {
          "name": "Burger Palace",
          "address": "Shop 123, ABC Mall, Mumbai",
          "coordinates": [72.8777, 19.0760],
          "distance": 1.2,
          "phone": "+************"
        },
        "customer": {
          "name": "John Doe",
          "address": "123 Main St, Mumbai",
          "coordinates": [72.8777, 19.0760],
          "distance": 2.5,
          "phone": "+919876543211"
        },
        "orderValue": 530,
        "deliveryFee": 25,
        "estimatedDistance": 3.7,
        "estimatedTime": 25,
        "priority": "normal",
        "paymentMethod": "online",
        "createdAt": "2024-12-01T12:00:00Z"
      }
    ],
    "totalAvailable": 5
  }
}
```

### **GET /orders**
Get assigned orders.

**Request:**
```bash
GET /api/v1/delivery-partner/orders
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `status` (optional): Filter by order status
- `date` (optional): Filter by date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-20241201-1234",
        "status": "picked_up",
        "restaurant": {
          "name": "Burger Palace",
          "address": "Shop 123, ABC Mall, Mumbai",
          "coordinates": [72.8777, 19.0760],
          "phone": "+************"
        },
        "customer": {
          "name": "John Doe",
          "address": "123 Main St, Mumbai",
          "coordinates": [72.8777, 19.0760],
          "phone": "+919876543211"
        },
        "items": [
          {
            "name": "Classic Burger",
            "quantity": 2
          }
        ],
        "orderValue": 530,
        "deliveryFee": 25,
        "paymentMethod": "online",
        "otp": {
          "restaurant": "1234",
          "customer": "5678"
        },
        "timeline": [
          {
            "status": "assigned",
            "timestamp": "2024-12-01T12:15:00Z"
          },
          {
            "status": "picked_up",
            "timestamp": "2024-12-01T12:45:00Z"
          }
        ],
        "estimatedDeliveryTime": "2024-12-01T13:30:00Z"
      }
    ],
    "summary": {
      "total": 3,
      "assigned": 1,
      "picked_up": 1,
      "delivered": 1
    }
  }
}
```

### **POST /orders/:orderId/accept**
Accept delivery order.

**Request:**
```bash
POST /api/v1/delivery-partner/orders/order_id/accept
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_id",
    "status": "assigned",
    "estimatedPickupTime": "2024-12-01T12:30:00Z"
  }
}
```

### **POST /orders/:orderId/reject**
Reject delivery order.

### **PUT /orders/:orderId/status**
Update delivery status.

**Request:**
```bash
PUT /api/v1/delivery-partner/orders/order_id/status
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "status": "picked_up",
  "otp": "1234",
  "location": [72.8777, 19.0760],
  "timestamp": "2024-12-01T12:45:00Z"
}
```

### **POST /orders/:orderId/pickup**
Confirm order pickup from restaurant.

**Request:**
```bash
POST /api/v1/delivery-partner/orders/order_id/pickup
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "otp": "1234",
  "location": [72.8777, 19.0760],
  "pickupTime": "2024-12-01T12:45:00Z"
}
```

### **POST /orders/:orderId/deliver**
Confirm order delivery to customer.

**Request:**
```bash
POST /api/v1/delivery-partner/orders/order_id/deliver
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "otp": "5678",
  "location": [72.8777, 19.0760],
  "deliveryTime": "2024-12-01T13:15:00Z",
  "customerRating": 5,
  "deliveryPhoto": "base64_image_data"
}
```

---

## 🗺️ **4. Navigation & Route**

### **GET /orders/:orderId/route**
Get delivery route and navigation.

**Request:**
```bash
GET /api/v1/delivery-partner/orders/order_id/route
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_id",
    "route": {
      "pickup": {
        "address": "Shop 123, ABC Mall, Mumbai",
        "coordinates": [72.8777, 19.0760],
        "distance": 1.2,
        "estimatedTime": 8
      },
      "delivery": {
        "address": "123 Main St, Mumbai",
        "coordinates": [72.8777, 19.0760],
        "distance": 2.5,
        "estimatedTime": 15
      },
      "totalDistance": 3.7,
      "totalTime": 23,
      "waypoints": [
        [72.8777, 19.0760],
        [72.8777, 19.0760]
      ],
      "instructions": [
        "Head north on Main Road",
        "Turn right at ABC Mall",
        "Destination will be on your left"
      ]
    },
    "traffic": {
      "condition": "moderate",
      "delay": 2
    }
  }
}
```

---

## 💰 **5. Earnings & Incentives**

### **GET /earnings**
Get earnings summary.

**Request:**
```bash
GET /api/v1/delivery-partner/earnings?period=weekly
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "weekly",
    "summary": {
      "totalEarnings": 3500,
      "baseEarnings": 2800,
      "incentives": 500,
      "tips": 200,
      "totalDeliveries": 45,
      "avgEarningsPerDelivery": 77.78
    },
    "breakdown": {
      "deliveryFees": 2250,
      "distanceBonus": 350,
      "timeBonus": 200,
      "peakHourBonus": 300,
      "weekendBonus": 200,
      "customerTips": 200
    },
    "dailyEarnings": [
      {
        "date": "2024-12-01",
        "earnings": 500,
        "deliveries": 6
      }
    ]
  }
}
```

### **GET /earnings/breakdown**
Get detailed earnings breakdown.

---

## 📊 **6. Performance Analytics**

### **GET /analytics/performance**
Get performance analytics.

**Request:**
```bash
GET /api/v1/delivery-partner/analytics/performance?period=monthly
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "monthly",
    "metrics": {
      "totalDeliveries": 150,
      "completionRate": 95.5,
      "avgDeliveryTime": 25,
      "customerRating": 4.5,
      "onTimeDeliveryRate": 92.3,
      "acceptanceRate": 88.7
    },
    "trends": [
      {
        "date": "2024-12-01",
        "deliveries": 6,
        "avgTime": 23,
        "rating": 4.6
      }
    ],
    "achievements": [
      {
        "title": "Speed Demon",
        "description": "Completed 10 deliveries under 20 minutes",
        "badge": "speed_badge"
      }
    ]
  }
}
```

---

## ⏰ **7. Availability & Schedule**

### **GET /availability**
Get availability schedule.

### **PUT /availability**
Update availability schedule.

**Request:**
```bash
PUT /api/v1/delivery-partner/availability
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "workingHours": {
    "monday": {"start": "09:00", "end": "18:00"},
    "tuesday": {"start": "09:00", "end": "18:00"},
    "sunday": {"available": false}
  },
  "preferredZones": ["zone_1", "zone_2"],
  "maxOrdersPerHour": 4
}
```

---

## 💳 **8. Settlements & Payouts**

### **GET /settlements**
Get settlement history.

### **POST /settlements/instant-payout**
Request instant payout.

**Request:**
```bash
POST /api/v1/delivery-partner/settlements/instant-payout
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "amount": 1000,
  "bankAccount": "account_id"
}
```

---

## 🆘 **9. Support & Emergency**

### **POST /emergency**
Send emergency alert.

**Request:**
```bash
POST /api/v1/delivery-partner/emergency
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "type": "accident",
  "location": [72.8777, 19.0760],
  "description": "Minor accident, need assistance",
  "contactNumber": "+************"
}
```

### **GET /support/chat**
Access support chat.

---

## 🔧 **Features & Capabilities**

### **✅ Implemented Features**
- **Real-time Location Tracking**: GPS-based location updates
- **Order Management**: Complete delivery lifecycle management
- **Earnings Tracking**: Detailed financial analytics and incentives
- **Performance Analytics**: Delivery metrics and achievements
- **Route Optimization**: Navigation and traffic-aware routing
- **Emergency Support**: Safety features and emergency alerts

### **🎯 Delivery Features**
- **Smart Order Assignment**: Distance and preference-based matching
- **OTP Verification**: Secure pickup and delivery confirmation
- **Real-time Tracking**: Live location sharing with customers
- **Incentive System**: Performance-based bonuses and rewards
- **Flexible Scheduling**: Custom availability and working hours

---

**🚚 The Delivery Partner API provides everything needed for efficient delivery operations and partner success!**
