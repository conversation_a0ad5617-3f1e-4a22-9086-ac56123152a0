# 👤 User App API Documentation

> **Customer-facing API for food ordering and profile management**

## 🎯 **Overview**

The User App API provides all functionality needed for customers to discover restaurants, place orders, manage their profiles, and track their order history. This API powers the customer mobile/web application.

---

## 🔐 **Authentication & Authorization**

### **Required Authentication**
```bash
Authorization: Bearer <jwt-token>
```

### **Authorized Role**
- **Customer**: All endpoints require customer role

---

## 📱 **API Endpoints**

### **Base URL**
```
http://localhost:3000/api/v1/user
```

---

## 👤 **1. Profile Management**

### **GET /profile**
Get current user profile information.

**Request:**
```bash
GET /api/v1/user/profile
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "phone": "+************",
    "role": "customer",
    "profile": {
      "name": "<PERSON>",
      "avatar": "https://example.com/avatar.jpg",
      "dateOfBirth": "1990-05-15",
      "gender": "male"
    },
    "preferences": {
      "cuisine": ["indian", "chinese", "italian"],
      "dietaryRestrictions": ["vegetarian"],
      "spiceLevel": "medium"
    },
    "stats": {
      "totalOrders": 45,
      "totalSpent": 2340.50,
      "favoriteRestaurants": 8,
      "avgOrderValue": 52.01
    },
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### **PUT /profile**
Update user profile information.

**Request:**
```bash
PUT /api/v1/user/profile
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "profile": {
    "name": "John Updated Doe",
    "avatar": "https://example.com/new-avatar.jpg",
    "dateOfBirth": "1990-05-15",
    "gender": "male"
  },
  "preferences": {
    "cuisine": ["indian", "chinese", "italian", "mexican"],
    "dietaryRestrictions": ["vegetarian"],
    "spiceLevel": "spicy"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "user_id",
    "profile": {
      "name": "John Updated Doe",
      "avatar": "https://example.com/new-avatar.jpg"
    }
  }
}
```

---

## 🍽️ **2. Restaurant Discovery**

### **GET /restaurants**
Discover restaurants with geospatial search and filtering.

**Request:**
```bash
GET /api/v1/user/restaurants
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `latitude` (required): User's latitude
- `longitude` (required): User's longitude
- `radius` (optional): Search radius in km (default: 5)
- `cuisine` (optional): Filter by cuisine type
- `minRating` (optional): Minimum rating filter
- `priceRange` (optional): `budget`, `mid-range`, `premium`
- `isVeg` (optional): `true` for vegetarian only
- `sortBy` (optional): `distance`, `rating`, `delivery_time`, `popularity`
- `limit` (optional): Number of results (default: 20)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "restaurants": [
      {
        "id": "restaurant_id",
        "name": "Burger Palace",
        "businessInfo": {
          "cuisine": ["american", "fast_food"],
          "description": "Best burgers in town",
          "priceRange": "mid-range"
        },
        "location": {
          "address": "Shop 123, ABC Mall, Mumbai",
          "coordinates": [72.8777, 19.0760],
          "distance": 1.2
        },
        "ratings": {
          "average": 4.2,
          "count": 150
        },
        "operationalInfo": {
          "isCurrentlyOpen": true,
          "acceptingOrders": true,
          "avgPreparationTime": 25,
          "deliveryTime": "30-40 mins",
          "minOrderAmount": 100
        },
        "offers": [
          {
            "title": "20% OFF",
            "description": "On orders above ₹300",
            "code": "SAVE20"
          }
        ],
        "popularItems": [
          {
            "name": "Classic Burger",
            "price": 250,
            "image": "https://example.com/burger.jpg"
          }
        ]
      }
    ],
    "pagination": {
      "total": 125,
      "page": 1,
      "limit": 20,
      "totalPages": 7
    },
    "filters": {
      "availableCuisines": ["indian", "chinese", "italian", "american"],
      "priceRanges": ["budget", "mid-range", "premium"],
      "avgDeliveryTime": "25-35 mins"
    }
  }
}
```

---

## 📦 **3. Order Management**

### **GET /orders**
Get user's order history with filtering.

**Request:**
```bash
GET /api/v1/user/orders
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `status` (optional): Filter by order status
- `restaurantId` (optional): Filter by restaurant
- `dateFrom` (optional): Start date (YYYY-MM-DD)
- `dateTo` (optional): End date (YYYY-MM-DD)
- `limit` (optional): Number of results (default: 10)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-*************",
        "restaurant": {
          "id": "restaurant_id",
          "name": "Burger Palace",
          "image": "https://example.com/restaurant.jpg"
        },
        "items": [
          {
            "name": "Classic Burger",
            "quantity": 2,
            "price": 250,
            "total": 500,
            "image": "https://example.com/burger.jpg"
          }
        ],
        "pricing": {
          "itemsSubtotal": 500,
          "deliveryFee": 25,
          "platformFee": 10,
          "taxes": 45,
          "discount": 50,
          "total": 530
        },
        "status": "delivered",
        "timeline": [
          {
            "status": "placed",
            "timestamp": "2024-12-01T12:00:00Z",
            "message": "Order placed successfully"
          },
          {
            "status": "delivered",
            "timestamp": "2024-12-01T13:15:00Z",
            "message": "Order delivered successfully"
          }
        ],
        "deliveryPartner": {
          "name": "Delivery Partner",
          "phone": "+919876543211",
          "rating": 4.5
        },
        "canReorder": true,
        "canRate": true,
        "canCancel": false,
        "createdAt": "2024-12-01T12:00:00Z",
        "deliveredAt": "2024-12-01T13:15:00Z"
      }
    ],
    "pagination": {
      "total": 45,
      "page": 1,
      "limit": 10,
      "totalPages": 5
    },
    "summary": {
      "totalOrders": 45,
      "totalSpent": 2340.50,
      "avgOrderValue": 52.01,
      "favoriteRestaurant": "Burger Palace"
    }
  }
}
```

### **POST /orders**
Place a new order.

**Request:**
```bash
POST /api/v1/user/orders
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "restaurantId": "restaurant_id",
  "items": [
    {
      "menuItemId": "item_id",
      "quantity": 2,
      "customizations": ["extra_cheese", "no_onions"],
      "specialInstructions": "Make it spicy"
    }
  ],
  "deliveryAddress": {
    "type": "home",
    "address": "123 Main St, Mumbai, Maharashtra",
    "coordinates": [72.8777, 19.0760],
    "landmark": "Near Metro Station",
    "contactPhone": "+************"
  },
  "paymentMethod": "online",
  "couponCode": "SAVE20",
  "specialInstructions": "Ring the bell twice"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order placed successfully",
  "data": {
    "orderId": "order_id",
    "orderNumber": "ORD-*************",
    "totalAmount": 530,
    "estimatedDeliveryTime": "2024-12-01T13:30:00Z",
    "paymentDetails": {
      "paymentUrl": "https://payment-gateway.com/pay/order_id",
      "orderId": "order_id"
    }
  }
}
```

---

## 📍 **4. Address Management**

### **GET /addresses**
Get user's saved addresses.

**Request:**
```bash
GET /api/v1/user/addresses
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "addresses": [
      {
        "id": "address_id",
        "type": "home",
        "address": "123 Main St, Mumbai, Maharashtra",
        "coordinates": [72.8777, 19.0760],
        "landmark": "Near Metro Station",
        "isDefault": true,
        "contactPhone": "+************",
        "deliveryInstructions": "Ring the bell twice"
      },
      {
        "id": "address_id_2",
        "type": "work",
        "address": "456 Office Complex, Mumbai, Maharashtra",
        "coordinates": [72.8777, 19.0760],
        "landmark": "Tower B, 5th Floor",
        "isDefault": false,
        "contactPhone": "+************"
      }
    ]
  }
}
```

### **POST /addresses**
Add a new address.

**Request:**
```bash
POST /api/v1/user/addresses
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "type": "work",
  "address": "456 Office Complex, Mumbai, Maharashtra",
  "coordinates": [72.8777, 19.0760],
  "landmark": "Tower B, 5th Floor",
  "isDefault": false,
  "contactPhone": "+************",
  "deliveryInstructions": "Call before delivery"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Address added successfully",
  "data": {
    "id": "address_id",
    "type": "work",
    "address": "456 Office Complex, Mumbai, Maharashtra",
    "coordinates": [72.8777, 19.0760],
    "isDefault": false
  }
}
```

---

## 🔧 **Features & Capabilities**

### **✅ Implemented Features**
- **Profile Management**: Complete user profile and preferences
- **Restaurant Discovery**: Geospatial search with advanced filtering
- **Order Placement**: Full order lifecycle with real-time tracking
- **Address Management**: Multiple saved addresses with delivery preferences
- **Order History**: Comprehensive order tracking and history
- **Reorder Functionality**: Quick reorder from previous orders

### **🎯 Customer Experience Features**
- **Smart Recommendations**: Based on order history and preferences
- **Real-time Tracking**: Live order status updates
- **Multiple Payment Options**: Online, COD, wallet support
- **Coupon System**: Discount codes and promotional offers
- **Rating & Reviews**: Restaurant and delivery partner feedback

### **📱 Mobile-Optimized**
- **Geolocation Integration**: Automatic location detection
- **Offline Support**: Cached data for better performance
- **Push Notifications**: Order updates and promotional offers
- **Quick Actions**: Reorder, track, rate, and support

---

## 🚀 **Usage Examples**

### **Discover Nearby Restaurants**
```bash
curl -X GET "http://localhost:3000/api/v1/user/restaurants?latitude=19.0760&longitude=72.8777&radius=5&cuisine=indian" \
  -H "Authorization: Bearer <jwt-token>"
```

### **Place an Order**
```bash
curl -X POST "http://localhost:3000/api/v1/user/orders" \
  -H "Authorization: Bearer <jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "restaurantId": "restaurant_id",
    "items": [{"menuItemId": "item_id", "quantity": 2}],
    "deliveryAddress": {...},
    "paymentMethod": "online"
  }'
```

### **Get Order History**
```bash
curl -X GET "http://localhost:3000/api/v1/user/orders?limit=20" \
  -H "Authorization: Bearer <jwt-token>"
```

---

**👤 The User App API provides everything customers need for a seamless food ordering experience!**
