import { apiService } from './api';
import { API_ENDPOINTS } from '../constants';
import type {
  DashboardStats,
  User,
  Order,
  Franchise,
  ApiResponse,
  UserFilters,
  OrderFilters,
  FranchiseFilters,
  PaginationResponse,
} from '../types';

export class DashboardService {
  // Get platform statistics
  static async getStats(params?: {
    period?: 'today' | 'week' | 'month' | 'year';
    franchiseId?: string;
  }): Promise<DashboardStats> {
    const response = await apiService.get<ApiResponse<DashboardStats>>(
      API_ENDPOINTS.DASHBOARD.STATS,
      { params }
    );
    return response.data;
  }

  // Get users with filtering and pagination
  static async getUsers(
    filters?: UserFilters
  ): Promise<{
    users: User[];
    pagination: PaginationResponse;
    analytics: {
      totalUsers: number;
      usersByRole: Record<string, number>;
      usersByStatus: Record<string, number>;
      growthMetrics: {
        newUsersToday: number;
        newUsersThisWeek: number;
        newUsersThisMonth: number;
      };
    };
  }> {
    const response = await apiService.get<
      ApiResponse<{
        users: User[];
        pagination: PaginationResponse;
        analytics: any;
      }>
    >(API_ENDPOINTS.DASHBOARD.USERS, { params: filters });
    return response.data;
  }

  // Get orders with filtering and pagination
  static async getOrders(
    filters?: OrderFilters
  ): Promise<{
    orders: Order[];
    pagination: PaginationResponse;
    analytics: {
      totalOrders: number;
      ordersByStatus: Record<string, number>;
      revenueMetrics: {
        totalRevenue: number;
        avgOrderValue: number;
        totalCommission: number;
      };
      performanceMetrics: {
        avgDeliveryTime: number;
        orderCompletionRate: number;
        customerSatisfactionRate: number;
      };
    };
  }> {
    const response = await apiService.get<
      ApiResponse<{
        orders: Order[];
        pagination: PaginationResponse;
        analytics: any;
      }>
    >(API_ENDPOINTS.DASHBOARD.ORDERS, { params: filters });
    return response.data;
  }

  // Get franchises with filtering and pagination
  static async getFranchises(
    filters?: FranchiseFilters
  ): Promise<{
    franchises: Franchise[];
    pagination: PaginationResponse;
    summary: {
      totalFranchises: number;
      activeFranchises: number;
      pendingFranchises: number;
      totalRevenue: number;
    };
  }> {
    const response = await apiService.get<
      ApiResponse<{
        franchises: Franchise[];
        pagination: PaginationResponse;
        summary: any;
      }>
    >(API_ENDPOINTS.DASHBOARD.FRANCHISES, { params: filters });
    return response.data;
  }

  // Get user by ID
  static async getUserById(userId: string): Promise<User> {
    const response = await apiService.get<ApiResponse<User>>(
      `${API_ENDPOINTS.DASHBOARD.USERS}/${userId}`
    );
    return response.data;
  }

  // Get order by ID
  static async getOrderById(orderId: string): Promise<Order> {
    const response = await apiService.get<ApiResponse<Order>>(
      `${API_ENDPOINTS.DASHBOARD.ORDERS}/${orderId}`
    );
    return response.data;
  }

  // Get franchise by ID
  static async getFranchiseById(franchiseId: string): Promise<Franchise> {
    const response = await apiService.get<ApiResponse<Franchise>>(
      `${API_ENDPOINTS.DASHBOARD.FRANCHISES}/${franchiseId}`
    );
    return response.data;
  }

  // Update user status
  static async updateUserStatus(
    userId: string,
    status: string
  ): Promise<User> {
    const response = await apiService.patch<ApiResponse<User>>(
      `${API_ENDPOINTS.DASHBOARD.USERS}/${userId}/status`,
      { status }
    );
    return response.data;
  }

  // Update order status
  static async updateOrderStatus(
    orderId: string,
    status: string
  ): Promise<Order> {
    const response = await apiService.patch<ApiResponse<Order>>(
      `${API_ENDPOINTS.DASHBOARD.ORDERS}/${orderId}/status`,
      { status }
    );
    return response.data;
  }

  // Get analytics data for charts
  static async getAnalytics(params: {
    type: 'revenue' | 'orders' | 'users';
    period: 'today' | 'week' | 'month' | 'year';
    franchiseId?: string;
  }): Promise<any> {
    const response = await apiService.get<ApiResponse<any>>(
      `${API_ENDPOINTS.DASHBOARD.STATS}/analytics`,
      { params }
    );
    return response.data;
  }

  // Export data
  static async exportData(params: {
    type: 'users' | 'orders' | 'franchises';
    format: 'csv' | 'excel';
    filters?: any;
  }): Promise<Blob> {
    const response = await apiService.get(
      `${API_ENDPOINTS.DASHBOARD.STATS}/export`,
      {
        params,
        responseType: 'blob',
      }
    );
    return response as unknown as Blob;
  }
}
