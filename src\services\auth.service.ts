import { apiService, authUtils } from './api';
import { API_ENDPOINTS } from '../constants';
import type {
  LoginRequest,
  LoginResponse,
  OtpVerificationRequest,
  AuthResponse,
  User,
  ApiResponse,
} from '../types';

export class AuthService {
  // Login user
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiService.post<ApiResponse<LoginResponse>>(
      API_ENDPOINTS.AUTH.LOGIN,
      credentials
    );
    return response.data;
  }

  // Verify OTP
  static async verifyOtp(otpData: OtpVerificationRequest): Promise<AuthResponse> {
    const response = await apiService.post<ApiResponse<AuthResponse>>(
      API_ENDPOINTS.AUTH.VERIFY_OTP,
      otpData
    );

    const { user, tokens } = response.data;

    // Store tokens and user data
    authUtils.setTokens(tokens.accessToken, tokens.refreshToken);
    authUtils.setUserData(user);

    return response.data;
  }

  // Refresh token
  static async refreshToken(): Promise<AuthResponse> {
    const refreshToken = authUtils.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiService.post<ApiResponse<AuthResponse>>(
      API_ENDPOINTS.AUTH.REFRESH_TOKEN,
      { refreshToken }
    );

    const { user, tokens } = response.data;

    // Update stored tokens and user data
    authUtils.setTokens(tokens.accessToken, tokens.refreshToken);
    authUtils.setUserData(user);

    return response.data;
  }

  // Get current user
  static async getCurrentUser(): Promise<User> {
    const response = await apiService.get<ApiResponse<User>>(
      API_ENDPOINTS.AUTH.ME
    );
    
    // Update stored user data
    authUtils.setUserData(response.data);
    
    return response.data;
  }

  // Logout
  static async logout(): Promise<void> {
    try {
      await apiService.post(API_ENDPOINTS.AUTH.LOGOUT);
    } catch (error) {
      // Even if logout fails on server, clear local data
      console.error('Logout error:', error);
    } finally {
      authUtils.logout();
    }
  }

  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return authUtils.isAuthenticated();
  }

  // Get stored user data
  static getStoredUser(): User | null {
    return authUtils.getUserData();
  }

  // Check if user has required role
  static hasRole(requiredRoles: string[]): boolean {
    const user = AuthService.getStoredUser();
    if (!user) return false;
    return requiredRoles.includes(user.role);
  }

  // Check if user is super admin
  static isSuperAdmin(): boolean {
    const user = AuthService.getStoredUser();
    return user?.role === 'super_admin';
  }

  // Check if user is franchise admin
  static isFranchiseAdmin(): boolean {
    const user = AuthService.getStoredUser();
    return user?.role === 'franchise_admin';
  }

  // Check if user can access admin features
  static canAccessAdmin(): boolean {
    return AuthService.isSuperAdmin() || AuthService.isFranchiseAdmin();
  }

  // Get user's franchise ID (for franchise admins)
  static getUserFranchiseId(): string | null {
    const user = AuthService.getStoredUser();
    return user?.franchiseId || null;
  }
}
