// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1';
export const APP_NAME = import.meta.env.VITE_APP_NAME || 'Food Delivery Admin Dashboard';
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';
export const FIXED_OTP = import.meta.env.VITE_OTP_FIXED || '123456';

// Local Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USER_DATA: 'userData',
  THEME: 'theme',
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    VERIFY_OTP: '/auth/verify-otp',
    REFRESH_TOKEN: '/auth/refresh-token',
    ME: '/auth/me',
    LOGOUT: '/auth/logout',
  },
  // Dashboard
  DASHBOARD: {
    STATS: '/dashboard/stats',
    FRANCHISES: '/dashboard/franchises',
    USERS: '/dashboard/users',
    ORDERS: '/dashboard/orders',
  },
} as const;

// User Roles
export const USER_ROLES = {
  CUSTOMER: 'customer',
  RESTAURANT_OWNER: 'restaurant_owner',
  DELIVERY_PARTNER: 'delivery_partner',
  FRANCHISE_ADMIN: 'franchise_admin',
  SUPER_ADMIN: 'super_admin',
} as const;

// User Status
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  PENDING_VERIFICATION: 'pending_verification',
} as const;

// Order Status
export const ORDER_STATUS = {
  PLACED: 'placed',
  ACCEPTED: 'accepted',
  PREPARING: 'preparing',
  READY: 'ready',
  PICKED_UP: 'pickedUp',
  OUT_FOR_DELIVERY: 'outForDelivery',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const;

// Order Status Colors
export const ORDER_STATUS_COLORS = {
  [ORDER_STATUS.PLACED]: '#1890ff',
  [ORDER_STATUS.ACCEPTED]: '#52c41a',
  [ORDER_STATUS.PREPARING]: '#faad14',
  [ORDER_STATUS.READY]: '#722ed1',
  [ORDER_STATUS.PICKED_UP]: '#13c2c2',
  [ORDER_STATUS.OUT_FOR_DELIVERY]: '#fa8c16',
  [ORDER_STATUS.DELIVERED]: '#52c41a',
  [ORDER_STATUS.CANCELLED]: '#ff4d4f',
} as const;

// Restaurant Status
export const RESTAURANT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended',
} as const;

// Franchise Status
export const FRANCHISE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  ONLINE: 'online',
  COD: 'cod',
  WALLET: 'wallet',
} as const;

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  PAGE_SIZE_OPTIONS: ['10', '20', '50', '100'],
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  DISPLAY_WITH_TIME: 'DD/MM/YYYY HH:mm',
  API: 'YYYY-MM-DD',
  API_WITH_TIME: 'YYYY-MM-DD HH:mm:ss',
} as const;

// Chart Colors
export const CHART_COLORS = [
  '#1890ff',
  '#52c41a',
  '#faad14',
  '#f5222d',
  '#722ed1',
  '#fa8c16',
  '#13c2c2',
  '#eb2f96',
  '#a0d911',
  '#fa541c',
] as const;

// Breakpoints (matching Ant Design)
export const BREAKPOINTS = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
} as const;

// Menu Items Configuration
export const MENU_ITEMS = [
  {
    key: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    permissions: [USER_ROLES.SUPER_ADMIN, USER_ROLES.FRANCHISE_ADMIN],
  },
  {
    key: 'users',
    label: 'User Management',
    path: '/users',
    permissions: [USER_ROLES.SUPER_ADMIN, USER_ROLES.FRANCHISE_ADMIN],
  },
  {
    key: 'restaurants',
    label: 'Restaurants',
    path: '/restaurants',
    permissions: [USER_ROLES.SUPER_ADMIN, USER_ROLES.FRANCHISE_ADMIN],
  },
  {
    key: 'orders',
    label: 'Orders',
    path: '/orders',
    permissions: [USER_ROLES.SUPER_ADMIN, USER_ROLES.FRANCHISE_ADMIN],
  },
  {
    key: 'franchises',
    label: 'Franchises',
    path: '/franchises',
    permissions: [USER_ROLES.SUPER_ADMIN],
  },
] as const;

// Status Badge Colors
export const STATUS_COLORS = {
  // User Status
  'user_active': 'success',
  'user_inactive': 'default',
  'user_suspended': 'error',
  'user_pending_verification': 'warning',

  // Restaurant Status
  'restaurant_active': 'success',
  'restaurant_inactive': 'default',
  'restaurant_pending': 'warning',
  'restaurant_suspended': 'error',

  // Franchise Status
  'franchise_active': 'success',
  'franchise_inactive': 'default',
  'franchise_pending': 'warning',

  // Payment Status
  'payment_completed': 'success',
  'payment_pending': 'warning',
  'payment_failed': 'error',
  'payment_refunded': 'default',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'Resource not found.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  LOGOUT_SUCCESS: 'Logged out successfully!',
  DATA_UPDATED: 'Data updated successfully!',
  DATA_DELETED: 'Data deleted successfully!',
  DATA_CREATED: 'Data created successfully!',
} as const;

// Loading Messages
export const LOADING_MESSAGES = {
  LOADING: 'Loading...',
  SAVING: 'Saving...',
  DELETING: 'Deleting...',
  UPDATING: 'Updating...',
  PROCESSING: 'Processing...',
} as const;

// Time Periods for Analytics
export const TIME_PERIODS = [
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' },
] as const;

// Export Types for Constants
export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
export type UserStatus = typeof USER_STATUS[keyof typeof USER_STATUS];
export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];
export type RestaurantStatus = typeof RESTAURANT_STATUS[keyof typeof RESTAURANT_STATUS];
export type FranchiseStatus = typeof FRANCHISE_STATUS[keyof typeof FRANCHISE_STATUS];
export type PaymentMethod = typeof PAYMENT_METHODS[keyof typeof PAYMENT_METHODS];
export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];
