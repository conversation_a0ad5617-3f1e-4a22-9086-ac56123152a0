# 🎛️ Dashboard API Documentation

> **Admin Dashboard API for Super Admin and Franchise Admin management**

## 🎯 **Overview**

The Dashboard API provides comprehensive administrative functionality for platform management, analytics, and oversight. It serves both Super Admin and Franchise Admin roles with appropriate access controls.

---

## 🔐 **Authentication & Authorization**

### **Required Authentication**
```bash
Authorization: Bearer <jwt-token>
```

### **Authorized Roles**
- **Super Admin**: Full platform access across all franchises
- **Franchise Admin**: Access limited to their specific franchise

---

## 📊 **API Endpoints**

### **Base URL**
```
http://localhost:3000/api/v1/dashboard
```

---

## 📈 **1. Platform Statistics**

### **GET /stats**
Get comprehensive platform analytics and metrics.

**Request:**
```bash
GET /api/v1/dashboard/stats
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `period` (optional): `today`, `week`, `month`, `year` (default: `today`)
- `franchiseId` (optional): Filter by specific franchise (Super Admin only)

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalUsers": 15420,
      "totalRestaurants": 1250,
      "totalDeliveryPartners": 890,
      "totalOrders": 45680,
      "totalRevenue": 2456780.50
    },
    "todayStats": {
      "newUsers": 45,
      "newOrders": 234,
      "revenue": 12450.75,
      "activeDeliveryPartners": 156
    },
    "orderStats": {
      "placed": 89,
      "accepted": 67,
      "preparing": 34,
      "ready": 12,
      "pickedUp": 8,
      "outForDelivery": 15,
      "delivered": 198,
      "cancelled": 6
    },
    "revenueBreakdown": {
      "grossRevenue": 12450.75,
      "platformCommission": 2240.14,
      "deliveryFees": 1890.50,
      "netRevenue": 10210.61
    },
    "topPerformers": {
      "restaurants": [
        {
          "id": "restaurant_id",
          "name": "Top Restaurant",
          "orders": 45,
          "revenue": 2340.50
        }
      ],
      "deliveryPartners": [
        {
          "id": "partner_id",
          "name": "Top Delivery Partner",
          "deliveries": 23,
          "rating": 4.8
        }
      ]
    }
  }
}
```

---

## 🏢 **2. Franchise Management**

### **GET /franchises**
Get franchise management data and analytics.

**Request:**
```bash
GET /api/v1/dashboard/franchises
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `status` (optional): `active`, `inactive`, `pending`
- `limit` (optional): Number of results (default: 10)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "franchises": [
      {
        "id": "franchise_id",
        "name": "Mumbai Central",
        "location": {
          "city": "Mumbai",
          "state": "Maharashtra",
          "coordinates": [72.8777, 19.0760]
        },
        "status": "active",
        "metrics": {
          "totalRestaurants": 125,
          "totalDeliveryPartners": 89,
          "totalOrders": 4568,
          "monthlyRevenue": 245670.50,
          "commissionRate": 0.18
        },
        "admin": {
          "name": "Franchise Admin Name",
          "email": "<EMAIL>",
          "phone": "+919876543210"
        },
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "limit": 10,
      "totalPages": 3
    },
    "summary": {
      "totalFranchises": 25,
      "activeFranchises": 23,
      "pendingFranchises": 2,
      "totalRevenue": 5678900.75
    }
  }
}
```

---

## 👥 **3. User Management**

### **GET /users**
Get user management data with filtering and analytics.

**Request:**
```bash
GET /api/v1/dashboard/users
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `role` (optional): `customer`, `restaurant_owner`, `delivery_partner`, `franchise_admin`
- `status` (optional): `active`, `inactive`, `suspended`, `pending_verification`
- `franchiseId` (optional): Filter by franchise
- `limit` (optional): Number of results (default: 10)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_id",
        "email": "<EMAIL>",
        "phone": "+919876543210",
        "role": "customer",
        "status": "active",
        "profile": {
          "name": "John Doe",
          "avatar": "https://example.com/avatar.jpg"
        },
        "franchiseId": "franchise_id",
        "stats": {
          "totalOrders": 45,
          "totalSpent": 2340.50,
          "avgOrderValue": 52.01,
          "lastOrderDate": "2024-12-01T15:30:00Z"
        },
        "createdAt": "2024-01-15T10:30:00Z",
        "lastActiveAt": "2024-12-01T18:45:00Z"
      }
    ],
    "pagination": {
      "total": 15420,
      "page": 1,
      "limit": 10,
      "totalPages": 1542
    },
    "analytics": {
      "totalUsers": 15420,
      "usersByRole": {
        "customer": 13450,
        "restaurant_owner": 1250,
        "delivery_partner": 890,
        "franchise_admin": 25
      },
      "usersByStatus": {
        "active": 14890,
        "inactive": 345,
        "suspended": 125,
        "pending_verification": 60
      },
      "growthMetrics": {
        "newUsersToday": 45,
        "newUsersThisWeek": 234,
        "newUsersThisMonth": 890
      }
    }
  }
}
```

---

## 📦 **4. Order Management**

### **GET /orders**
Get comprehensive order management data and analytics.

**Request:**
```bash
GET /api/v1/dashboard/orders
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `status` (optional): Order status filter
- `franchiseId` (optional): Filter by franchise
- `restaurantId` (optional): Filter by restaurant
- `customerId` (optional): Filter by customer
- `deliveryPartnerId` (optional): Filter by delivery partner
- `dateFrom` (optional): Start date filter (YYYY-MM-DD)
- `dateTo` (optional): End date filter (YYYY-MM-DD)
- `limit` (optional): Number of results (default: 10)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-20241201-1234",
        "customer": {
          "id": "customer_id",
          "name": "John Doe",
          "phone": "+919876543210"
        },
        "restaurant": {
          "id": "restaurant_id",
          "name": "Burger Palace",
          "location": "Mumbai Central"
        },
        "deliveryPartner": {
          "id": "partner_id",
          "name": "Delivery Partner",
          "phone": "+919876543211"
        },
        "items": [
          {
            "name": "Classic Burger",
            "quantity": 2,
            "price": 250,
            "total": 500
          }
        ],
        "pricing": {
          "itemsSubtotal": 500,
          "deliveryFee": 25,
          "platformFee": 10,
          "taxes": 45,
          "total": 580
        },
        "status": "delivered",
        "timeline": [
          {
            "status": "placed",
            "timestamp": "2024-12-01T12:00:00Z"
          },
          {
            "status": "delivered",
            "timestamp": "2024-12-01T13:15:00Z"
          }
        ],
        "deliveryAddress": {
          "address": "123 Main St, Mumbai",
          "coordinates": [72.8777, 19.0760]
        },
        "paymentMethod": "online",
        "paymentStatus": "completed",
        "createdAt": "2024-12-01T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 45680,
      "page": 1,
      "limit": 10,
      "totalPages": 4568
    },
    "analytics": {
      "totalOrders": 45680,
      "ordersByStatus": {
        "placed": 89,
        "accepted": 67,
        "preparing": 34,
        "ready": 12,
        "pickedUp": 8,
        "outForDelivery": 15,
        "delivered": 44890,
        "cancelled": 565
      },
      "revenueMetrics": {
        "totalRevenue": 2456780.50,
        "avgOrderValue": 53.78,
        "totalCommission": 442220.49
      },
      "performanceMetrics": {
        "avgDeliveryTime": 28.5,
        "orderCompletionRate": 98.2,
        "customerSatisfactionRate": 4.3
      }
    }
  }
}
```

---

## 🔧 **Features & Capabilities**

### **✅ Implemented Features**
- **Real-time Analytics**: Live platform metrics and KPIs
- **Multi-franchise Support**: Franchise-specific data filtering
- **User Management**: Comprehensive user analytics and management
- **Order Oversight**: Complete order lifecycle monitoring
- **Revenue Tracking**: Detailed financial analytics and reporting
- **Performance Metrics**: Platform and partner performance tracking

### **🎯 Business Intelligence**
- **Growth Analytics**: User acquisition and retention metrics
- **Revenue Analytics**: Commission tracking and financial reporting
- **Operational Analytics**: Order fulfillment and delivery performance
- **Partner Analytics**: Restaurant and delivery partner performance

### **🔒 Security Features**
- **Role-based Access**: Super Admin vs Franchise Admin permissions
- **Data Isolation**: Franchise-specific data access controls
- **Audit Logging**: Administrative action tracking
- **Secure Authentication**: JWT-based authentication with proper authorization

---

## 🚀 **Usage Examples**

### **Get Today's Platform Stats**
```bash
curl -X GET "http://localhost:3000/api/v1/dashboard/stats?period=today" \
  -H "Authorization: Bearer <jwt-token>"
```

### **Get Active Users**
```bash
curl -X GET "http://localhost:3000/api/v1/dashboard/users?status=active&limit=20" \
  -H "Authorization: Bearer <jwt-token>"
```

### **Get Recent Orders**
```bash
curl -X GET "http://localhost:3000/api/v1/dashboard/orders?limit=50" \
  -H "Authorization: Bearer <jwt-token>"
```

---

**🎛️ The Dashboard API provides complete administrative control over your food delivery platform!**
