# 🍽️ Restaurant Partner API Documentation

> **Restaurant management API for restaurant owners and operators**

## 🎯 **Overview**

The Restaurant Partner API provides comprehensive restaurant management functionality including menu management, order processing, sales analytics, and earnings tracking. This API powers the restaurant partner mobile/web application.

---

## 🔐 **Authentication & Authorization**

### **Required Authentication**
```bash
Authorization: Bearer <jwt-token>
```

### **Authorized Role**
- **Restaurant Owner**: All endpoints require restaurant owner role

---

## 🏪 **API Endpoints**

### **Base URL**
```
http://localhost:3000/api/v1/restaurant-partner
```

---

## 👤 **1. Profile Management**

### **GET /profile**
Get restaurant partner profile and business overview.

**Request:**
```bash
GET /api/v1/restaurant-partner/profile
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "phone": "+919876543210",
      "role": "restaurant_owner"
    },
    "profile": {
      "name": "Restaurant Owner",
      "businessType": "restaurant",
      "experience": 5,
      "documents": {
        "fssai": "verified",
        "gst": "verified",
        "tradeLicense": "pending"
      }
    },
    "restaurants": ["restaurant_id_1", "restaurant_id_2"],
    "performance": {
      "rating": 4.5,
      "totalOrders": 1250,
      "acceptanceRate": 95.5
    },
    "earnings": {
      "todayEarnings": 2500,
      "monthlyEarnings": 75000,
      "totalEarnings": 450000
    }
  }
}
```

### **PUT /profile**
Update restaurant partner profile.

**Request:**
```bash
PUT /api/v1/restaurant-partner/profile
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "profile": {
    "name": "Updated Restaurant Owner",
    "phone": "+919876543210",
    "restaurantInfo": {
      "businessType": "restaurant",
      "experience": 6
    }
  }
}
```

---

## 🏪 **2. Restaurant Management**

### **GET /restaurants**
Get owned restaurants list.

**Request:**
```bash
GET /api/v1/restaurant-partner/restaurants
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "restaurants": [
      {
        "id": "restaurant_id",
        "name": "Burger Palace",
        "businessInfo": {
          "cuisine": ["american", "fast_food"],
          "description": "Best burgers in town"
        },
        "location": {
          "address": "Shop 123, ABC Mall, Mumbai",
          "coordinates": [72.8777, 19.0760]
        },
        "operationalInfo": {
          "isCurrentlyOpen": true,
          "acceptingOrders": true,
          "avgPreparationTime": 25
        },
        "performance": {
          "rating": 4.2,
          "totalOrders": 150,
          "acceptanceRate": 95.5
        },
        "status": "active"
      }
    ],
    "totalRestaurants": 2,
    "activeRestaurants": 2
  }
}
```

### **POST /restaurants**
Add a new restaurant.

**Request:**
```bash
POST /api/v1/restaurant-partner/restaurants
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "New Restaurant",
  "businessInfo": {
    "cuisine": ["indian", "north_indian"],
    "description": "Authentic North Indian cuisine",
    "businessType": "restaurant"
  },
  "location": {
    "address": "Shop 456, XYZ Complex, Mumbai",
    "coordinates": [72.8777, 19.0760]
  }
}
```

### **PUT /restaurants/:restaurantId**
Update restaurant details.

**Request:**
```bash
PUT /api/v1/restaurant-partner/restaurants/restaurant_id
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "Updated Restaurant Name",
  "businessInfo": {
    "cuisine": ["indian", "north_indian", "punjabi"],
    "description": "Updated description"
  },
  "location": {
    "address": "Updated address",
    "coordinates": [72.8777, 19.0760]
  }
}
```

---

## 📋 **3. Menu Management**

### **GET /restaurants/:restaurantId/menu**
Get restaurant menu.

**Request:**
```bash
GET /api/v1/restaurant-partner/restaurants/restaurant_id/menu
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "restaurantId": "restaurant_id",
    "menu": [
      {
        "id": "item_id",
        "name": "Classic Burger",
        "description": "Juicy beef patty with fresh vegetables",
        "price": 250,
        "category": "Main Course",
        "isAvailable": true,
        "preparationTime": 15,
        "isVeg": false,
        "image": "https://example.com/burger.jpg",
        "customizations": [
          {
            "name": "Extra Cheese",
            "price": 25
          }
        ]
      }
    ],
    "categories": ["Starters", "Main Course", "Beverages", "Desserts"]
  }
}
```

### **POST /restaurants/:restaurantId/menu**
Add menu item.

**Request:**
```bash
POST /api/v1/restaurant-partner/restaurants/restaurant_id/menu
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "name": "New Menu Item",
  "description": "Delicious new item",
  "price": 300,
  "category": "Main Course",
  "isAvailable": true,
  "preparationTime": 20,
  "isVeg": true,
  "ingredients": ["ingredient1", "ingredient2"],
  "customizations": [
    {
      "name": "Extra Spicy",
      "price": 0
    }
  ]
}
```

### **PUT /restaurants/:restaurantId/menu/:itemId**
Update menu item.

### **DELETE /restaurants/:restaurantId/menu/:itemId**
Delete menu item.

---

## 📦 **4. Order Management**

### **GET /orders**
Get restaurant orders.

**Request:**
```bash
GET /api/v1/restaurant-partner/orders
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `status` (optional): Filter by order status
- `date` (optional): Filter by date (YYYY-MM-DD)
- `restaurantId` (optional): Filter by specific restaurant
- `limit` (optional): Number of results (default: 10)
- `page` (optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order_id",
        "orderNumber": "ORD-20241201-1234",
        "customer": {
          "name": "John Doe",
          "phone": "+919876543210"
        },
        "items": [
          {
            "name": "Classic Burger",
            "quantity": 2,
            "price": 250,
            "total": 500,
            "customizations": ["extra_cheese"]
          }
        ],
        "pricing": {
          "itemsSubtotal": 500,
          "total": 500
        },
        "status": "placed",
        "specialInstructions": "Make it spicy",
        "estimatedPreparationTime": 25,
        "createdAt": "2024-12-01T12:00:00Z"
      }
    ],
    "summary": {
      "total": 15,
      "pending": 3,
      "preparing": 5,
      "ready": 2,
      "completed": 5
    }
  }
}
```

### **PUT /orders/:orderId/status**
Update order status.

**Request:**
```bash
PUT /api/v1/restaurant-partner/orders/order_id/status
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "status": "accepted",
  "estimatedTime": 25
}
```

### **POST /orders/:orderId/accept**
Accept order.

### **POST /orders/:orderId/reject**
Reject order with reason.

---

## 📊 **5. Analytics & Reports**

### **GET /analytics/sales**
Get sales analytics.

**Request:**
```bash
GET /api/v1/restaurant-partner/analytics/sales?period=weekly
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `period` (optional): `daily`, `weekly`, `monthly`, `yearly`
- `restaurantId` (optional): Filter by restaurant

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "weekly",
    "summary": {
      "totalRevenue": 25000,
      "totalOrders": 150,
      "avgOrderValue": 167,
      "commission": 2500,
      "netEarnings": 22500
    },
    "trends": [
      {
        "date": "2024-12-01",
        "revenue": 3500,
        "orders": 21
      }
    ],
    "topItems": [
      {
        "name": "Classic Burger",
        "orders": 45,
        "revenue": 11250
      }
    ]
  }
}
```

### **GET /analytics/performance**
Get performance analytics.

---

## 💰 **6. Earnings & Settlements**

### **GET /earnings**
Get earnings summary.

**Request:**
```bash
GET /api/v1/restaurant-partner/earnings?period=monthly
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "period": "monthly",
    "summary": {
      "todayEarnings": 2500,
      "weeklyEarnings": 17500,
      "monthlyEarnings": 75000,
      "totalEarnings": 450000,
      "pendingSettlement": 5000
    },
    "breakdown": {
      "grossRevenue": 75000,
      "commission": 13500,
      "platformFee": 1500,
      "taxes": 2250,
      "netEarnings": 57750
    },
    "settlements": [
      {
        "id": "settlement_id",
        "amount": 15000,
        "date": "2024-11-30",
        "status": "completed"
      }
    ]
  }
}
```

### **GET /settlements**
Get settlement history.

---

## ⚙️ **7. Restaurant Operations**

### **PUT /restaurants/:restaurantId/status**
Update restaurant operational status.

**Request:**
```bash
PUT /api/v1/restaurant-partner/restaurants/restaurant_id/status
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "status": "open",
  "acceptingOrders": true
}
```

### **PUT /restaurants/:restaurantId/hours**
Update restaurant operating hours.

**Request:**
```bash
PUT /api/v1/restaurant-partner/restaurants/restaurant_id/hours
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "hours": {
    "monday": {"open": "09:00", "close": "22:00"},
    "tuesday": {"open": "09:00", "close": "22:00"},
    "sunday": {"closed": true}
  }
}
```

---

## 🔧 **Features & Capabilities**

### **✅ Implemented Features**
- **Restaurant Management**: Complete restaurant CRUD operations
- **Menu Management**: Dynamic menu updates with categories and customizations
- **Order Processing**: Real-time order management with status updates
- **Sales Analytics**: Comprehensive sales reporting and trends
- **Earnings Tracking**: Detailed financial analytics and settlement management
- **Operational Controls**: Restaurant status and hours management

### **🎯 Business Features**
- **Multi-restaurant Support**: Manage multiple restaurant locations
- **Real-time Order Notifications**: Instant order alerts
- **Performance Metrics**: Acceptance rate, preparation time, ratings
- **Financial Reporting**: Revenue, commission, and settlement tracking
- **Menu Optimization**: Item performance and popularity analytics

---

**🍽️ The Restaurant Partner API provides complete restaurant management capabilities for food delivery success!**
