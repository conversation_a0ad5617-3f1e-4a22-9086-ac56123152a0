@import"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap";/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial}}}.fixed{position:fixed}.static{position:static}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}:root{--color-primary:#1890ff;--color-success:#52c41a;--color-warning:#faad14;--color-error:#ff4d4f;--color-text:#000000d9;--color-text-secondary:#00000073;--color-background:#fff;--color-background-secondary:#fafafa;--color-border:#d9d9d9;font-family:Inter,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;font-weight:400;line-height:1.5}*{box-sizing:border-box}html,body{background-color:var(--color-background-secondary);min-height:100vh;color:var(--color-text);margin:0;padding:0}#root{min-height:100vh}::-webkit-scrollbar{width:6px;height:6px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.text-truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.shadow-card{box-shadow:0 1px 2px #00000008,0 1px 6px -1px #00000005,0 2px 4px #00000005}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.animate-spin{animation:1s linear infinite spin}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.animate-fade-in{animation:.3s ease-out fadeIn}.ant-layout{min-height:100vh}.ant-layout-sider{box-shadow:2px 0 8px #1d23290d}.ant-menu-dark{background:#001529}.ant-menu-dark .ant-menu-item-selected{background-color:var(--color-primary)}.ant-card{border-radius:8px;box-shadow:0 1px 2px #00000008,0 1px 6px -1px #00000005}.ant-table-thead>tr>th{background-color:#fafafa;font-weight:600}.ant-statistic-title{color:var(--color-text-secondary);font-size:14px;font-weight:500}.ant-statistic-content{color:var(--color-text);font-weight:600}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}#root{max-width:1280px;margin:0 auto;padding:2rem;text-align:center}.logo{height:6em;padding:1.5em;will-change:filter;transition:filter .3s}.logo:hover{filter:drop-shadow(0 0 2em #646cffaa)}.logo.react:hover{filter:drop-shadow(0 0 2em #61dafbaa)}@keyframes logo-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (prefers-reduced-motion: no-preference){a:nth-of-type(2) .logo{animation:logo-spin infinite 20s linear}}.card{padding:2em}.read-the-docs{color:#888}
