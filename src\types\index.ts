// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// User Types
export interface User {
  id: string;
  email: string;
  phone: string;
  role: UserRole;
  status: UserStatus;
  profile: UserProfile;
  franchiseId?: string;
  stats?: UserStats;
  createdAt: string;
  lastActiveAt?: string;
}

export interface UserProfile {
  name: string;
  avatar?: string;
}

export interface UserStats {
  totalOrders: number;
  totalSpent: number;
  avgOrderValue: number;
  lastOrderDate?: string;
}

export type UserRole = 'customer' | 'restaurant_owner' | 'delivery_partner' | 'franchise_admin' | 'super_admin';
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending_verification';

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  requiresOtp: boolean;
}

export interface OtpVerificationRequest {
  email: string;
  otp: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Dashboard Types
export interface DashboardStats {
  overview: {
    totalUsers: number;
    totalRestaurants: number;
    totalDeliveryPartners: number;
    totalOrders: number;
    totalRevenue: number;
  };
  todayStats: {
    newUsers: number;
    newOrders: number;
    revenue: number;
    activeDeliveryPartners: number;
  };
  orderStats: {
    placed: number;
    accepted: number;
    preparing: number;
    ready: number;
    pickedUp: number;
    outForDelivery: number;
    delivered: number;
    cancelled: number;
  };
  revenueBreakdown: {
    grossRevenue: number;
    platformCommission: number;
    deliveryFees: number;
    netRevenue: number;
  };
  topPerformers: {
    restaurants: TopRestaurant[];
    deliveryPartners: TopDeliveryPartner[];
  };
}

export interface TopRestaurant {
  id: string;
  name: string;
  orders: number;
  revenue: number;
}

export interface TopDeliveryPartner {
  id: string;
  name: string;
  deliveries: number;
  rating: number;
}

// Restaurant Types
export interface Restaurant {
  id: string;
  name: string;
  location: {
    address: string;
    city: string;
    state: string;
    coordinates: [number, number];
  };
  status: RestaurantStatus;
  metrics: RestaurantMetrics;
  owner: {
    name: string;
    email: string;
    phone: string;
  };
  createdAt: string;
}

export interface RestaurantMetrics {
  totalOrders: number;
  monthlyRevenue: number;
  rating: number;
  avgDeliveryTime: number;
}

export type RestaurantStatus = 'active' | 'inactive' | 'pending' | 'suspended';

// Order Types
export interface Order {
  id: string;
  orderNumber: string;
  customer: {
    id: string;
    name: string;
    phone: string;
  };
  restaurant: {
    id: string;
    name: string;
    location: string;
  };
  deliveryPartner?: {
    id: string;
    name: string;
    phone: string;
  };
  items: OrderItem[];
  pricing: OrderPricing;
  status: OrderStatus;
  timeline: OrderTimeline[];
  deliveryAddress: {
    address: string;
    coordinates: [number, number];
  };
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  createdAt: string;
}

export interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
}

export interface OrderPricing {
  itemsSubtotal: number;
  deliveryFee: number;
  platformFee: number;
  taxes: number;
  total: number;
}

export interface OrderTimeline {
  status: OrderStatus;
  timestamp: string;
}

export type OrderStatus = 'placed' | 'accepted' | 'preparing' | 'ready' | 'pickedUp' | 'outForDelivery' | 'delivered' | 'cancelled';
export type PaymentMethod = 'online' | 'cod' | 'wallet';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

// Franchise Types
export interface Franchise {
  id: string;
  name: string;
  location: {
    city: string;
    state: string;
    coordinates: [number, number];
  };
  status: FranchiseStatus;
  metrics: FranchiseMetrics;
  admin: {
    name: string;
    email: string;
    phone: string;
  };
  createdAt: string;
}

export interface FranchiseMetrics {
  totalRestaurants: number;
  totalDeliveryPartners: number;
  totalOrders: number;
  monthlyRevenue: number;
  commissionRate: number;
}

export type FranchiseStatus = 'active' | 'inactive' | 'pending';

// Filter Types
export interface UserFilters extends PaginationParams {
  role?: UserRole;
  status?: UserStatus;
  franchiseId?: string;
}

export interface OrderFilters extends PaginationParams {
  status?: OrderStatus;
  franchiseId?: string;
  restaurantId?: string;
  customerId?: string;
  deliveryPartnerId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface RestaurantFilters extends PaginationParams {
  status?: RestaurantStatus;
  franchiseId?: string;
  city?: string;
}

export interface FranchiseFilters extends PaginationParams {
  status?: FranchiseStatus;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesData {
  date: string;
  value: number;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// Loading States
export interface LoadingState {
  loading: boolean;
  error: string | null;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'number' | 'date';
  required?: boolean;
  options?: { label: string; value: string }[];
  placeholder?: string;
}

// Navigation Types
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  permissions?: UserRole[];
}
